import CouponOfWeekCard from './CouponOfWeekCard/CouponOfWeekCard';
import couponOfWeekStyles from './CouponOfWeek.module.scss';
import { HorizontalDragScroll } from 'components';

const CouponOfTheWeek = ({ coupons }) => {
  return (
    <>
      {/* Desktop version - now with horizontal drag scroll */}
      <div className="d-none d-md-block">
        <HorizontalDragScroll className={couponOfWeekStyles.desktopScrollContainer}>
          {coupons.map((couponItem, index) => (
            <div 
              key={`desktop-${couponItem.id || index}`}
              className={couponOfWeekStyles.desktopCardItem}
            >
              <CouponOfWeekCard coupon={couponItem} />
            </div>
          ))}
        </HorizontalDragScroll>
      </div>
      
      {/* Mobile version - native scroll (no drag component needed) */}
      <div className="d-md-none">
        <div className={couponOfWeekStyles.mobileScrollContainer}>
          {coupons.map((couponItem, index) => (
            <div 
              key={`mobile-${couponItem.id || index}`}
              className={couponOfWeekStyles.mobileCardItem}
            >
              <CouponOfWeekCard coupon={couponItem} />
            </div>
          ))}
      </div>
      </div>
    </>
  );
};

export default CouponOfTheWeek;