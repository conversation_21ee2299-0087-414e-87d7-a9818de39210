@import '../../styles/sass/variables';
@import '../../styles/sass/mixins';

.cardContainer {
  border-radius: 15px;
  overflow: hidden;
  margin: 15px 0;

  @include mediaMobile {
    width: 85%;
    min-width: 280px;
    margin-right: 10px;
    flex: 0 0 auto;
    scroll-snap-align: start;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.imageContainer {
  position: relative;
  margin: 4px;

  @include mediaMobile {
    img {
      width: 175px !important;
      height: 195px !important;
      object-fit: cover;
    }
  }
}

.discountBadge {
  width: 90px;
  min-width: 15%;
  padding: 0 20px;
  border-radius: 0 14px;
  background-color: #000;
  color: #fff;
  font-size: 25px;
  font-weight: 900;
  position: absolute;
  top: 0px;
  right: 0;
  height: 48px;
  justify-content: center;
  align-items: center;
  z-index: 5;
  display: flex;

  @include mediaMobile {
    font-size: 23px;
    height: 36px;
    padding: 0 10px;
    min-width: auto;
    width: auto;
  }
}

.logoContainer {
  position: absolute;
  height: 120px;
  width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 20px;
  bottom: 30%;
}

.logoWrapper {
  @include mediaMobile {
    margin-top: 0px;
    width: 112px;
    height: 54px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.logo {
  width: 80px;
  height: auto;

  @include mediaMobile {
    width: 71px !important;
    height: 45px !important;
  }
}

.description {
  font-size: 12px;
  font-weight: bold;
}

.description {
  font-weight: 500;
  font-size: 16px;

  @include mediaMobile {
    font-size: 12px;
    margin-top: 10px;
    margin-bottom: 10px;
    line-height: 1.2;
  }
}

.button {
  font-size: 18px;
  font-weight: 700;
  width: 100%;

  p {
    padding: 4px;
  }

  @include mediaMobile {
    font-size: 11px;
    margin: 0px;
    line-height: 25px;

    p {
      padding: 0px;
    }
  }
}

.desktopCardItem {
  flex: 0 0 auto;
  margin-right: 30px;

  // Desktop card sizing - you can adjust based on your design
  width: 568px;
  min-width: 568px;
  
  &:last-child {
    margin-right: 0;
  }
}

// Mobile scroll container - native scroll only
.mobileScrollContainer {
  display: flex;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  -webkit-overflow-scrolling: touch;

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}
