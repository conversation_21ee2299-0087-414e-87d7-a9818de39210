import { useRouter } from 'next/navigation';
import styles from './../CouponOfWeek.module.scss';
import { displayCouponCode } from '../../../util/helpers';

const CouponOfWeekCard = ({ coupon }) => {
  const router = useRouter();

  // Extract data from the coupon of the week structure
  const couponData = coupon?.coupon || {};
  const brandName = coupon?.brandName || couponData?.brandName;
  const brandLogo = coupon?.brandLogo || couponData?.brandLogo;
  const discountValue = couponData?.discountValue;
  const discountType = couponData?.discountType;
  const image = couponData?.image;
  const couponLink = couponData?.couponLink || '#';
  const shortDescription = couponData?.shortCouponDescription || '';

  // Format discount value based on discount type
  const formattedDiscount = discountType === 'PERCENT' ? `${discountValue}%` : `${discountValue}€`;

  return (
    <div className={`${styles.cardContainer} card`}>
      <div className="row">
        <div className="col-6 pr-1">
          <div className={styles.imageContainer}>
            <img
              src={image}
              alt={`${brandName} coupon image`}
              width={1200}
              height={800}
              style={{ width: '280', height: '380px', borderRadius: '11px', objectFit: 'cover', objectPosition: 'center' }}
            />
            <div
              className={`${styles.discountBadge}`}
              style={{ padding: '11px' }}
            >
              {formattedDiscount}
            </div>
          </div>
        </div>
        <div className="col-6 d-flex flex-column align-items-center justify-content-around pl-md-4 pr-md-5 py-md-3 pl-1 pr-4 py-2">
          <div className={styles.logoWrapper}>
            <img
              src={brandLogo}
              alt={`${brandName} logo`}
              width={100}
              height={100}
              className={styles.logo}
              style={{ width: '120px', height: '80px', objectFit: 'contain' }}
            />
          </div>
          <div className={`text-center ${styles.description}`}>
            {shortDescription}
          </div>
          <div
            className={`rabatteButton couponCodeBtn ${styles.button}`}
            onClick={() => {
              // Use the brand slug from the coupon or extract it from the coupon link
              const brandSlug = coupon?.brandSlug ||
                (couponLink && couponLink.split('/').pop()) ||
                brandName.toLowerCase().replace(/\s+/g, '-');
              displayCouponCode(brandSlug, router);
            }}
          >
            <p>{coupon?.code ? 'Code kopieren' : 'Gutscheincode'}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CouponOfWeekCard;
