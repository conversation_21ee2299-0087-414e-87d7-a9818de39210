@import '../../styles/sass/variables';
@import '../../styles/sass/mixins';

.testedForYouContainer {
  width: 100%;
}

.testedCard {
  border-radius: 10px;
  overflow: hidden;
  margin: 15px 0px;

  @media (max-width: 767px) {
    height: auto;
  }
}

.mobileScrollContainer {
  display: flex;
  overflow-x: auto;
  padding: 0 10px;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.mobileCard {
  border-radius: 10px;
  overflow: hidden;
  min-width: 170px;
  margin-right: 10px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.imageContainer {
  position: relative;
  padding: 4px;
  min-height: 480px;
  height: 100%;

  img {
    object-fit: cover;
    width: 100%;
  }

  @media (max-width: 767px) {
    margin: 2px;
    min-height: 195px;
  }
}

.discountBadge {
  width: 90px;
  min-width: 15%;
  padding: 0 20px;
  border-radius: 0 14px;
  background-color: #000;
  color: #fff;
  font-size: 35px; /* Updated font size */
  font-weight: 900;
  position: absolute;
  top: 0px;
  right: 0;
  height: 48px;
  justify-content: center;
  align-items: center;
  z-index: 5;
  display: flex;

  @media (max-width: 767px) {
    font-size: 22px;
    height: 36px;
    padding: 0 10px;
    min-width: auto;
    width: auto;
  }
}

.logoWrapper {
  align-self: flex-start;
  margin-top: 15px;
  margin-left: 15px;

  @media (max-width: 767px) {
    margin-top: 10px;
    margin-left: 10px;
  }
}

.logo {
  width: 80px;
  height: auto;

  @media (max-width: 767px) {
    width: 61px !important;
    height: 38px !important;
  }
}

.descriptionContainer {
  text-align: left;
  margin-bottom: 15px;
  padding: 0 15px;
  align-self: flex-start;
  width: 100%;
}

.descriptionTitle {
  font-weight: 900;
  font-size: 35px;
  margin-bottom: 5px;
  line-height: 40px;

  @media (max-width: 767px) {
    font-size: 22px;
    margin-bottom: 5px;
  }
}

.descriptionContent {
  font-weight: 400;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 10px;
  white-space: pre-line;

  strong {
    font-weight: 700;
    display: block;
    margin-bottom: 3px;
  }

  p {
    margin-top: 5px;
    margin-bottom: 0;
  }

  @media (max-width: 767px) {
    font-size: 10px;
    margin-bottom: 8px;
  }
}

.button {
  font-size: 16px;
  width: 100%;
  height: 34px;
  border-radius: 20px;
  margin: 0 15px 15px;
  width: calc(100% - 30px);
  align-self: center;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  color: #fff;
  line-height: 34px;

  &:hover {
    background-color: #81e9f0;
    color: #000;
    text-decoration: none;
    border: none;
    outline: none;
  }

  @media (max-width: 767px) {
    font-size: 11px;
    margin: 0 10px 10px;
    width: calc(100% - 20px);
    line-height: 30px;
    height: 30px;
  }
}
