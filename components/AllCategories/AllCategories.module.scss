@import '../../styles/sass/mixins';

.scrollContainer {
  display: flex;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  -webkit-overflow-scrolling: touch;
  cursor: grab;
  user-select: none;

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.cardItem {
  flex: 0 0 auto;
  width: calc(22% - 20px);
  min-width: 270px;
  margin: 0 10px;
  display: flex;

  /* Ensure all cards have the same height */
  & > div {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Make the card component take full height */
  :global(.category-card) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure the card inner content takes full height */
  :global(.category-card > div) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  @include mediaMobile {
    min-width: 170px;
    margin-right: 10px;
    margin-left: 0;
    margin-top: 0;
    margin-bottom: 0;
  }
}

/* Category image container */
.categoryImage {
  height: 280px !important;
  min-height: 280px !important;
  border-radius: 15px !important;

  @include mediaMobile {
    height: 180px !important;
    min-height: 180px !important;
  }

  /* Ensure images maintain aspect ratio and cover the container */
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 15px !important;
  }
}

/* Category content container */
.categoryContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 15px 15px 0px 15px;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0 0 15px 15px;
  margin-top: -5px;
  position: relative;
  z-index: 2;

  @include mediaMobile {
    padding: 12px 10px 8px 10px;
  }

  /* Category name styling - targeting the Card.Description component */
  div {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin: 0;
    line-height: 1.3;

    @include mediaMobile {
      font-size: 16px;
    }
  }
}