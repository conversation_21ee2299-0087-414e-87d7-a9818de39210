import styles from './AllCategories.module.scss';
import {
  Card,
  HorizontalDragScroll
} from 'components';

const AllCategories = ({ categories }) => {

  return (
    <div className="horizontal-scroll-wrapper">
      <HorizontalDragScroll className={styles.scrollContainer}>
        {categories &&
          categories.map((category, index) => {
            return (
              <div
                key={index}
                className={styles.cardItem}
              >
                <Card
                  className="category-card white-card"
                  cardClassName="border-grey categories-card"
                >
                  <Card.Image
                    url={`/rabatte/${category.slug}`}
                    src={category.compressedImage || category.image}
                    alt={`${category.name} category`}
                    eager={index < 4}
                    className={styles.categoryImage}
                  />
                  <div className={styles.categoryContent}>
                    <Card.Description>
                      {category.name}
                    </Card.Description>
                  </div>
                </Card>
              </div>
            );
          })}
      </HorizontalDragScroll>
    </div>
  );
}

export default AllCategories;