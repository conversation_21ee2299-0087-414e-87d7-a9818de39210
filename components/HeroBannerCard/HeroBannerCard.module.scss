// import required since mixins are not working from global
@import '../../styles/sass/mixins';

.imageContainer {
  position: relative;
  margin: 4px;

  @include mediaMobile {
    img {
      width: 405px !important;
      height: 196px !important;
    }
  }
}

.discountBadge {
  min-width: 12%;
  padding: 20px;
  border-radius: 0 11px;
  background-color: #000;
  color: #fff;
  font-size: 45px;
  font-weight: 900;
  position: absolute;
  top: 0px;
  right: 0;
  height: 60px;
  justify-content: center;
  align-items: center;
  z-index: 5;
  display: flex;

  @include mediaMobile {
    font-size: 20px;
    width: 55px;
    height: 28px;
  }
}

.logoContainer {
  position: absolute;
  height: 120px;
  width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 20px;
  bottom: 30%;

  @include mediaMobile {
    width: 112px;
    height: 54px;
    border-radius: 10px;
    bottom: 60%;
    left: 5%;
  }
}

.logo {
  width: 80px;
  height: 80px;
  object-fit: contain;

  @include mediaMobile {
    width: 70px;
    height: 45px;
  }
}

.description {
  font-size: 22px;
  font-weight: bold;

  @include mediaMobile {
    font-size: 12px;
  }
}