import React, { useRef, useState, useEffect } from 'react';
import styles from './HorizontalDragScroll.module.scss';

const HorizontalDragScroll = ({ 
  children, 
  className = '', 
  scrollSpeed = 2,
  style = {} 
}) => {
  const scrollContainerRef = useRef(null);
  const [isMouseDown, setIsMouseDown] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  // Mouse events
  const handleMouseDown = (e) => {
    if (!scrollContainerRef.current) return;
    setIsMouseDown(true);
    setStartX(e.pageX);
    setScrollLeft(scrollContainerRef.current.scrollLeft);
    scrollContainerRef.current.style.cursor = 'grabbing';
    e.preventDefault();
  };

  const handleMouseUp = () => {
    setIsMouseDown(false);
    if (scrollContainerRef.current) {
      scrollContainerRef.current.style.cursor = 'grab';
    }
  };

  const handleMouseMove = (e) => {
    if (!isMouseDown || !scrollContainerRef.current) return;
    const x = e.pageX;
    const walk = (x - startX) * scrollSpeed;
    scrollContainerRef.current.scrollLeft = scrollLeft - walk;
    e.preventDefault();
  };

  // Touch events for mobile
  const handleTouchStart = (e) => {
    if (!scrollContainerRef.current) return;
    setIsMouseDown(true);
    setStartX(e.touches[0].clientX);
    setScrollLeft(scrollContainerRef.current.scrollLeft);
  };

  const handleTouchMove = (e) => {
    if (!isMouseDown || !scrollContainerRef.current) return;
    const x = e.touches[0].clientX;
    const walk = (x - startX) * scrollSpeed;
    scrollContainerRef.current.scrollLeft = scrollLeft - walk;
  };

  // Document mouse up listener
  useEffect(() => {
    const handleMouseUpDocument = () => {
      setIsMouseDown(false);
      if (scrollContainerRef.current) {
        scrollContainerRef.current.style.cursor = 'grab';
      }
    };

    document.addEventListener('mouseup', handleMouseUpDocument);
    return () => {
      document.removeEventListener('mouseup', handleMouseUpDocument);
    };
  }, []);

  const containerStyle = {
    display: 'flex',
    overflowX: 'auto',
    scrollbarWidth: 'none', // Firefox
    msOverflowStyle: 'none', // IE/Edge
    WebkitOverflowScrolling: 'touch',
    cursor: 'grab',
    userSelect: 'none',
    ...style
  };

  return (
    <div
      className={`${styles.scrollContainer} ${className}`}
      ref={scrollContainerRef}
      style={containerStyle}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseUp}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleMouseUp}
    >
      {typeof children === 'function' 
        ? children(isMouseDown) 
        : React.Children.map(children, (child, index) => 
            React.cloneElement(child, {
              key: child.key || index,
              style: {
                ...child.props.style,
                pointerEvents: isMouseDown ? 'none' : 'auto'
              }
            })
          )
      }
      <style jsx>{`
        div::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
};

export default HorizontalDragScroll;