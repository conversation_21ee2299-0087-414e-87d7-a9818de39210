import { useState } from 'react';
import styles from './FeaturedCategoryCoupon.module.scss';
import {
  Like,
  Card,
  HorizontalDragScroll
} from 'components';
import { displayCouponCode } from 'components/Card/Card';
import LimitOverlay, {
  getOverlayMessage,
} from 'components/LimitOverlay/LimitOverlay';
import { toggleCouponFavorite } from 'util/helpers';
import { getCouponAltText } from 'util/helpers';
import useAPIError from 'components/APIErrorNotification/useAPIError';

const FeaturedCategoryCoupons = ({ coupons }) => {
  const { addMessage } = useAPIError();
  const [localCoupons, setLocalCoupons] = useState(coupons);

  return (
    <div className="horizontal-scroll-wrapper">
      <HorizontalDragScroll className={styles.scrollContainer}>
        {localCoupons &&
          localCoupons.map((item, index) => {
            return (
              <div
                key={index}
                className={styles.cardItem}
              >
                <Card
                  className="coupon-card-landing-page white-card"
                  cardClassName="border-grey"
                >
                  <Card.Image
                    url={`/brand/${item.brandSlug}`}
                    src={item.compressedImage || item.image}
                    alt={getCouponAltText(item)}
                    eager={index < 4}
                    className={styles.fixedHeightImage}
                  >
                    <Like
                      isLiked={item.isFavourite}
                      id={item.id}
                      onSuccess={() =>
                        setLocalCoupons((prevCoupons) =>
                          toggleCouponFavorite(prevCoupons, item.id)
                        )
                      }
                      onError={(_, data) => {
                        addMessage(data, 'error');
                      }}
                    />
                    {item?.status !== 'NO_ACTIVE_SUBSCRIPTION' && (
                      <LimitOverlay
                        message={getOverlayMessage(item)}
                        className="border-rounded-top-14"
                      />
                    )}
                    <Card.Discount
                      type={item.discountType}
                      shortDescription={item.shortDescription}
                    >
                      {item.discountValue}
                    </Card.Discount>
                  </Card.Image>
                  <div className={styles.cardContent}>
                    <Card.ClientLogo
                      src={item?.compressedBrandLogo || item?.brandLogo}
                      alt={`Logo der Brand ${item.brandSlug || item.brandName} gutschein`}
                    />
                    <Card.Description>
                      {item.shortCouponDescription}
                    </Card.Description>
                    <div className={styles.buttonWrapper}>
                      <Card.CouponButton
                        coupon={item}
                        getCouponCode={() => {
                          const brandSlug = item.brandSlug || createSlugFromBrandName(item.brandName) || item.brandId;
                          displayCouponCode(brandSlug);
                        }}
                      >
                        <p>{item.code ? 'Code kopieren' : 'Gutscheincode'}</p>
                      </Card.CouponButton>
                    </div>
                  </div>
                </Card>
              </div>
            );
          })}
      </HorizontalDragScroll>
    </div>
  );
}

export default FeaturedCategoryCoupons;