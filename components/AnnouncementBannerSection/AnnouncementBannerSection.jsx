import { Section } from '../../components';
import Link from 'next/link';
import styles from './AnnouncementBannerSection.module.scss';

const AnnouncementBanner = ({
  banner = null
}) => {

  // Extract data from the banner object if available
  const content = banner?.content;
  const isButton = banner?.isButton !== undefined ? banner.isButton : true;
  const buttonText = banner?.buttonText || "Gutscheincode";
  const buttonLink = banner?.buttonLink || "#";
  // Prefer compressed image if available, otherwise use regular image
  const image = banner?.compressedImage || banner?.image;

  // Determine if we should show an image
  const showImage = !!image && banner?.image;

  return (
    <Section
      background="#81E9F0"
      customPadding={true}
      rowClass=''
      className=''
      containerClass='container'
    >
      {showImage ? (
        <>
          <div className="col-12 col-md-3">
            <img
              src={image}
              alt="Announcement banner image"
              className={styles.bannerImage}
            />
          </div>
          <div className="col-12 col-md-9 pad-tb-5 pl-4">
            <div className={styles.contentContainer}>
              <div
                className={styles.content}
                dangerouslySetInnerHTML={{ __html: content }}
              />
              {isButton && (
                <Link
                  href={buttonLink}
                  className={`rabatteButton couponCodeBtn ${styles.button}`}
                >
                  <p>{buttonText}</p>
                </Link>
              )}
            </div>
          </div>
        </>
      ) : (
        <div className="col-12 col-md-8 offset-md-2 text-center pad-tb-5">
          <div className={`${styles.contentContainer} ${styles.centered}`}>
            <div
              className={styles.content}
              dangerouslySetInnerHTML={{ __html: content }}
            />
            {isButton && (
              <Link
                href={buttonLink}
                className={`rabatteButton couponCodeBtn ${styles.button}`}
              >
                <p>{buttonText}</p>
              </Link>
            )}
          </div>
        </div>
      )}
    </Section>
  )
}

export default AnnouncementBanner;