@import '../../styles/sass/variables';
@import '../../styles/sass/mixins';

.bannerContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: $handheld-breakpoint) {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
}

.imageContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto;

  @media (max-width: $handheld-breakpoint) {
    max-width: 250px;
    margin: 0 auto 1rem auto;
  }
}

.bannerImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.contentContainer {
  text-align: left;

  &.centered {
    text-align: center;
    align-items: center;

    .title, .description {
      text-align: center;
    }

    .button {
      margin: 1rem auto 0;
    }
  }

  @media (max-width: $handheld-breakpoint) {
    text-align: center;
    align-items: center;
    padding: 0 1rem;
  }
}

.content {
  margin-bottom: 1rem;

  p {
    margin-bottom: 0.5rem;
  }

  strong {
    font-size: 2.5rem;
    font-weight: 900;
    line-height: 1.2;
    display: block;
    margin-bottom: 0.5rem;

    @media (max-width: $handheld-breakpoint) {
      font-size: 1.8rem;
    }
  }

  p:not(:first-child) {
    font-size: 1.1rem;
    line-height: 1.5;

    @media (max-width: $handheld-breakpoint) {
      font-size: 1rem;
    }
  }
}

.button {
  p {
    margin-bottom: 0rem;
    background-color: #000;
    border-radius: 50px;

    &:hover {
      background-color: #81e9f0;
      color: #000;
      cursor: pointer;
      text-decoration: none;
    }
  }

  &:hover {
    text-decoration: none;
  }
}