// import required since mixins are not working from global
@import '../../styles/sass/mixins';

.brandCarousel {
  width: 100%;
  position: relative;

  @include mediaMobile {
    width: 90%;
  }

  &:hover {
    :global(.carousel-control-next-icon),
    :global(.carousel-control-prev-icon) {
      background-color: rgba(0, 0, 0, 0.3);
    }
  }

  :global(.carousel-indicators) {
    bottom: -50px;

    @include mediaMobile {
      bottom: -40px;
    }

    li {
      background-color: #000;
    }
  }

  :global(.carousel-control-next) {
    width: 5%;
    right: 1%;
    color: #fff;
    opacity: 1;
    background-color: transparent;

    @include mediaMobile {
      right: 5%;
      z-index: 10;
    }

    @include mediaTablet {
      right: -3%;
    }

    @include mediaLaptop {
      right: -3%;
    }

    :global(.carousel-control-next-icon) {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e");
      background-color: transparent;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-size: 50%;
      background-position: center;
      transition: background-color 0.3s ease;
      filter: drop-shadow(0px 0px 2px rgba(0, 0, 0, 0.5));

      @include mediaMobile {
        width: 25px;
        height: 25px;
      }
    }
  }

  :global(.carousel-control-prev) {
    width: 5%;
    left: 1%;
    color: #fff;
    opacity: 1;
    background-color: transparent;

    @include mediaMobile {
      left: 5%;
      z-index: 10;
    }

    @include mediaTablet {
      left: -3%;
    }

    @include mediaLaptop {
      left: -3%;
    }

    :global(.carousel-control-prev-icon) {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e");
      background-color: transparent;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-size: 50%;
      background-position: center;
      transition: background-color 0.3s ease;
      filter: drop-shadow(0px 0px 2px rgba(0, 0, 0, 0.5));

      @include mediaMobile {
        width: 25px;
        height: 25px;
      }
    }
  }
}

.brandLogoContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;

  @include mediaMobile {
    justify-content: space-around;
  }
  @include mediaTablet {
    justify-content: space-around;
  }

  :global(img) {
    cursor: pointer;
    width: 14.6%;
    height: 23%;
    margin: 1% 1%;
    padding: 20px;

    @include mediaMobile {
      width: 30%;
      padding: 25px 10px;
    }
    @include mediaTablet {
      width: 20%;
      padding: 25px 10px;
    }
  }
}

.heroBannerCard {
  border-radius: 15px;
  overflow: hidden;
}
