import { Card, InlineItem, Layout, Section } from '../components';
import Link from 'next/link';
import { useContext } from 'react';
import MainAppContext from 'context/mainAppContext';

const CategoriesSection = ({ all, sectionBg = '#fff', className = '' }) => {
  const { categories } = useContext(MainAppContext);
  // setShowCodeModel(true);
  const categoriesPerRow = all || 4;
  let popularCat = [];

  for (let i = 0, item; (item = categories[i]); i++) {
    if (popularCat.length >= categoriesPerRow) break;

    if (item.isPopular || categoriesPerRow === 'all') {
      popularCat = [
        ...popularCat,
        <Card
          className="col-6 col-lg-3"
          cardClassName="categories-card"
          key={item.id}
        >
          <Card.Image url={`rabatte/${item.slug || item.id}`} src={item.image} alt={item.name} />
          <Card.Description>
            <h3>{item.name}</h3>
          </Card.Description>
        </Card>,
      ];
    }
  }
  return (
    <Section
      className={`${className}`}
      background={sectionBg}
      customPadding={true}
    >
      <h2 className="col-sm-12 section-header pad-sm-b-1 text-left">
        Kategorien
      </h2>
      {popularCat}
      {all !== 'all' && (
        <>
          <div className="container">
            <div className="row">
              {categories &&
                categories.map((item, index) => {
                  return (
                    <InlineItem
                      label={item.name}
                      key={item.id}
                      url={`rabatte/${item.slug || item.id}`}
                      className="col-lg-3 col-sm-6"
                    >
                      {/*<FaPlane />*/}
                      <img src={item.icon} alt={`Categories ${item.name} of id ${item.id}`} loading="lazy" />
                    </InlineItem>
                  );
                })}
            </div>
          </div>
          <div className="col-sm-12 pad-t-4 d-flex justify-content-center">
            <Link href="kategorien">
              <a className="btn btn-dark">Alle Kategorien von A-Z</a>
            </Link>
          </div>
        </>
      )}
    </Section>
  );
};
export default CategoriesSection;
